'use client'

import React, { useState } from 'react'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { 
  X, 
  Package, 
  Edit, 
  Save, 
  Trash2, 
  User,
  Calendar,
  MapPin,
  Tag,
  BarChart3,
  Clock
} from 'lucide-react'
import { Item, ItemStatus, Category, Location } from '@/types'

// Enhanced status system with visual indicators
const getStatusConfig = (status: ItemStatus, isLoaned = false) => {
  if (isLoaned) {
    return {
      label: 'Dipinjam',
      color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      dotColor: 'bg-yellow-400',
      bgColor: '#facc15'
    }
  }

  switch (status) {
    case ItemStatus.AVAILABLE:
      return {
        label: 'Tersedia',
        color: 'bg-green-100 text-green-800 border-green-200',
        dotColor: 'bg-green-400',
        bgColor: '#22c55e'
      }
    case ItemStatus.OUT_OF_STOCK:
      return {
        label: 'Perbaikan',
        color: 'bg-red-100 text-red-800 border-red-200',
        dotColor: 'bg-red-400',
        bgColor: '#ef4444'
      }
    case ItemStatus.DISCONTINUED:
      return {
        label: 'Pensiun',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        dotColor: 'bg-gray-400',
        bgColor: '#6b7280'
      }
    default:
      return {
        label: 'Unknown',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        dotColor: 'bg-gray-400',
        bgColor: '#6b7280'
      }
  }
}

interface InventorySidebarProps {
  isOpen: boolean
  onClose: () => void
  item: Item | null
  categories: Category[]
  locations: Location[]
  onUpdateItem: (itemId: string, data: any) => Promise<void>
  onDeleteItem: (itemId: string, itemName: string) => Promise<void>
  isLoading: boolean
}

const InventorySidebar: React.FC<InventorySidebarProps> = ({
  isOpen,
  onClose,
  item,
  categories,
  locations,
  onUpdateItem,
  onDeleteItem,
  isLoading
}) => {
  const [isEditMode, setIsEditMode] = useState(false)
  const [editData, setEditData] = useState<any>({})

  if (!isOpen || !item) return null

  const statusConfig = getStatusConfig(item.status,
    item.borrowings && item.borrowings.some((borrowing: any) => borrowing.status === 'ACTIVE')
  )

  const handleEdit = () => {
    setIsEditMode(true)
    setEditData({
      name: item.name,
      description: item.description,
      categoryId: item.categoryId,
      locationId: item.locationId,
      stock: item.stock,
      minStock: item.minStock
    })
  }

  const handleSave = async () => {
    try {
      await onUpdateItem(item.id, editData)
      setIsEditMode(false)
    } catch (error) {
      console.error('Error updating item:', error)
    }
  }

  const handleCancel = () => {
    setIsEditMode(false)
    setEditData({})
  }

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity"
        onClick={onClose}
      />

      {/* Sidebar */}
      <div className="fixed right-0 top-0 h-full w-96 bg-white shadow-2xl z-50 transform transition-transform overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 p-4 flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">Detail Item</h2>
          <div className="flex items-center space-x-2">
            {!isEditMode && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleEdit}
                className="flex items-center space-x-1"
              >
                <Edit className="h-4 w-4" />
                <span>Edit</span>
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {isEditMode ? (
            /* Edit Mode */
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nama Item
                </label>
                <Input
                  value={editData.name || ''}
                  onChange={(e) => setEditData({ ...editData, name: e.target.value })}
                  placeholder="Masukkan nama item"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Deskripsi
                </label>
                <textarea
                  value={editData.description || ''}
                  onChange={(e) => setEditData({ ...editData, description: e.target.value })}
                  placeholder="Masukkan deskripsi item"
                  rows={3}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Kategori
                  </label>
                  <select
                    value={editData.categoryId || ''}
                    onChange={(e) => setEditData({ ...editData, categoryId: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Pilih Kategori</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Lokasi
                  </label>
                  <select
                    value={editData.locationId || ''}
                    onChange={(e) => setEditData({ ...editData, locationId: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Pilih Lokasi</option>
                    {locations.map((location) => (
                      <option key={location.id} value={location.id}>
                        {location.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Stok Saat Ini
                  </label>
                  <Input
                    type="number"
                    value={editData.stock || 0}
                    onChange={(e) => setEditData({ ...editData, stock: parseInt(e.target.value) || 0 })}
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Stok Minimum
                  </label>
                  <Input
                    type="number"
                    value={editData.minStock || 0}
                    onChange={(e) => setEditData({ ...editData, minStock: parseInt(e.target.value) || 0 })}
                    min="0"
                  />
                </div>
              </div>

              {/* Edit Actions */}
              <div className="flex space-x-3 pt-4 border-t border-gray-200">
                <Button
                  variant="primary"
                  onClick={handleSave}
                  disabled={isLoading}
                  className="flex-1 flex items-center justify-center space-x-2"
                >
                  <Save className="h-4 w-4" />
                  <span>Simpan</span>
                </Button>
                <Button
                  variant="secondary"
                  onClick={handleCancel}
                  className="flex-1"
                >
                  Batal
                </Button>
              </div>
            </div>
          ) : (
            /* View Mode */
            <div className="space-y-4">
              <div>
                <h3 className="text-xl font-bold text-gray-900">{item.name}</h3>
                <p className="text-gray-600 mt-1">{item.description || 'Tidak ada deskripsi'}</p>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="bg-gray-50 p-3 rounded-lg">
                  <span className="font-medium text-gray-700 block">ID Item</span>
                  <span className="text-gray-900 font-mono text-xs">{item.id}</span>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <span className="font-medium text-gray-700 block">Kategori</span>
                  <span className="text-gray-900">{item.category?.name}</span>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <span className="font-medium text-gray-700 block">Lokasi</span>
                  <span className="text-gray-900">{item.location?.name}</span>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <span className="font-medium text-gray-700 block">Stok</span>
                  <span className={`text-gray-900 font-semibold ${
                    item.stock === 0 ? 'text-red-600' :
                    item.stock <= item.minStock ? 'text-yellow-600' :
                    'text-green-600'
                  }`}>
                    {item.stock} / {item.minStock} min
                  </span>
                </div>
              </div>

              {/* Status */}
              <div className="bg-white border border-gray-200 p-4 rounded-lg">
                <span className="font-medium text-gray-700 block mb-2">Status Saat Ini</span>
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${statusConfig.dotColor}`}></div>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium border ${statusConfig.color}`}>
                    {statusConfig.label}
                  </span>
                </div>
              </div>

              {/* Borrowing Info */}
              {item.borrowings && item.borrowings.some((borrowing: any) => borrowing.status === 'ACTIVE') && (
                <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
                  <h4 className="font-medium text-yellow-800 mb-2">Informasi Peminjaman</h4>
                  {item.borrowings
                    .filter((borrowing: any) => borrowing.status === 'ACTIVE')
                    .map((borrowing: any) => (
                      <div key={borrowing.id} className="space-y-1">
                        <p className="text-sm text-yellow-700">
                          <span className="font-medium">Peminjam:</span> {borrowing.borrowerName || 'Tidak diketahui'}
                        </p>
                        <p className="text-sm text-yellow-700">
                          <span className="font-medium">Tujuan:</span> {borrowing.purpose || 'Tidak diketahui'}
                        </p>
                        <p className="text-sm text-yellow-700">
                          <span className="font-medium">Jatuh Tempo:</span> {
                            borrowing.expectedReturnDate ?
                            new Date(borrowing.expectedReturnDate).toLocaleDateString('id-ID') :
                            'Tidak diketahui'
                          }
                        </p>
                      </div>
                    ))}
                </div>
              )}

              {!isEditMode && (
                <div className="pt-4 border-t border-gray-200">
                  <Button
                    variant="danger"
                    className="w-full"
                    loading={isLoading}
                    onClick={() => onDeleteItem(item.id, item.name)}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Hapus Item
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  )
}

export default InventorySidebar
