import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { ActivityType } from '@/types'

// GET /api/activities - Get activities with optional filters for reporting
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')
    const type = searchParams.get('type')
    const itemId = searchParams.get('itemId')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '100')

    interface WhereClause {
      createdAt?: {
        gte?: Date;
        lt?: Date;
      };
      type?: ActivityType;
      itemId?: string;
    }

    const where: WhereClause = {}

    // Date range filter
    if (dateFrom || dateTo) {
      where.createdAt = {}
      if (dateFrom) {
        where.createdAt.gte = new Date(dateFrom)
      }
      if (dateTo) {
        // Add 1 day to include the entire end date
        const endDate = new Date(dateTo)
        endDate.setDate(endDate.getDate() + 1)
        where.createdAt.lt = endDate
      }
    }

    // Activity type filter
    if (type && Object.values(ActivityType).includes(type as ActivityType)) {
      where.type = type as ActivityType
    }

    // Item filter
    if (itemId) {
      where.itemId = itemId
    }

    const [activities, total] = await Promise.all([
      prisma.activity.findMany({
        where,
        include: {
          item: {
            include: {
              category: true,
              location: true
            }
          },
          borrowing: {
            select: {
              borrowerName: true,
              purpose: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.activity.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: activities,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching activities:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch activities' },
      { status: 500 }
    )
  }
}
