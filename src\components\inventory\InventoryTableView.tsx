'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { Package, Eye } from 'lucide-react'
import { Item, ItemStatus, Category } from '@/types'

// Enhanced status system with visual indicators
const getStatusConfig = (status: ItemStatus, isLoaned = false) => {
  if (isLoaned) {
    return {
      label: 'Dipinjam',
      color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      dotColor: 'bg-yellow-400',
      bgColor: '#facc15'
    }
  }

  switch (status) {
    case ItemStatus.AVAILABLE:
      return {
        label: 'Tersedia',
        color: 'bg-green-100 text-green-800 border-green-200',
        dotColor: 'bg-green-400',
        bgColor: '#22c55e'
      }
    case ItemStatus.OUT_OF_STOCK:
      return {
        label: 'Perbaikan',
        color: 'bg-red-100 text-red-800 border-red-200',
        dotColor: 'bg-red-400',
        bgColor: '#ef4444'
      }
    case ItemStatus.DISCONTINUED:
      return {
        label: 'Pensiun',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        dotColor: 'bg-gray-400',
        bgColor: '#6b7280'
      }
    default:
      return {
        label: 'Unknown',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        dotColor: 'bg-gray-400',
        bgColor: '#6b7280'
      }
  }
}

interface InventoryTableViewProps {
  items: Item[]
  selectedItems: Set<string>
  onSelectAll: (checked: boolean) => void
  onSelectItem: (itemId: string, checked: boolean) => void
  onOpenDetail: (item: Item) => void
  editingField: { itemId: string, field: string } | null
  setEditingField: (field: { itemId: string, field: string } | null) => void
  hoveredItem: string | null
  setHoveredItem: (itemId: string | null) => void
  categories: Category[]
  onUpdateItem: () => void
}

const InventoryTableView: React.FC<InventoryTableViewProps> = ({
  items,
  selectedItems,
  onSelectAll,
  onSelectItem,
  onOpenDetail,
  editingField,
  setEditingField,
  hoveredItem,
  setHoveredItem,
  categories,
  onUpdateItem
}) => {
  const allSelected = items.length > 0 && items.every(item => selectedItems.has(item.id))
  const someSelected = items.some(item => selectedItems.has(item.id))

  const handleInlineEdit = async (itemId: string, field: string, value: string) => {
    try {
      const response = await fetch(`/api/items/${itemId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ [field]: value })
      })
      if (response.ok) {
        onUpdateItem()
      }
    } catch (error) {
      console.error('Error updating item:', error)
    }
    setEditingField(null)
  }

  return (
    <Card className="glass">
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="w-12 px-4 py-3">
                  <input
                    type="checkbox"
                    checked={allSelected}
                    ref={(el) => {
                      if (el) el.indeterminate = someSelected && !allSelected
                    }}
                    onChange={(e) => onSelectAll(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Nama Item</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Kategori</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Aksi</th>
              </tr>
            </thead>
            <tbody>
              {items.map((item) => {
                const statusConfig = getStatusConfig(item.status,
                  item.borrowings && item.borrowings.some((borrowing: any) => borrowing.status === 'ACTIVE')
                )

                return (
                  <tr
                    key={item.id}
                    className="border-b border-gray-100 hover:bg-gray-50 transition-colors relative"
                    onMouseEnter={() => setHoveredItem(item.id)}
                    onMouseLeave={() => setHoveredItem(null)}
                  >
                    <td className="px-4 py-3">
                      <input
                        type="checkbox"
                        checked={selectedItems.has(item.id)}
                        onChange={(e) => onSelectItem(item.id, e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                          <Package className="h-5 w-5 text-gray-500" />
                        </div>
                        <div>
                          {editingField?.itemId === item.id && editingField?.field === 'name' ? (
                            <input
                              type="text"
                              defaultValue={item.name}
                              autoFocus
                              onBlur={(e) => handleInlineEdit(item.id, 'name', e.target.value)}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  handleInlineEdit(item.id, 'name', e.currentTarget.value)
                                }
                                if (e.key === 'Escape') {
                                  setEditingField(null)
                                }
                              }}
                              className="font-medium text-gray-900 bg-white border border-blue-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                          ) : (
                            <div
                              className="font-medium text-gray-900 cursor-pointer hover:text-blue-600 transition-colors"
                              onClick={() => setEditingField({ itemId: item.id, field: 'name' })}
                            >
                              {item.name}
                            </div>
                          )}
                          <div className="text-sm text-gray-500">{item.description}</div>
                        </div>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      {editingField?.itemId === item.id && editingField?.field === 'category' ? (
                        <select
                          defaultValue={item.categoryId}
                          autoFocus
                          onBlur={(e) => handleInlineEdit(item.id, 'categoryId', e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              handleInlineEdit(item.id, 'categoryId', e.currentTarget.value)
                            }
                            if (e.key === 'Escape') {
                              setEditingField(null)
                            }
                          }}
                          className="bg-white border border-blue-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          {categories.map(cat => (
                            <option key={cat.id} value={cat.id}>{cat.name}</option>
                          ))}
                        </select>
                      ) : (
                        <span
                          className="cursor-pointer hover:text-blue-600 transition-colors"
                          onClick={() => setEditingField({ itemId: item.id, field: 'category' })}
                        >
                          {item.category?.name}
                        </span>
                      )}
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${statusConfig.dotColor}`}></div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium border ${statusConfig.color}`}>
                          {statusConfig.label}
                        </span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onOpenDetail(item)}
                        className="flex items-center space-x-1"
                      >
                        <Eye className="h-4 w-4" />
                        <span>Detail</span>
                      </Button>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </CardContent>

      {/* Quick Preview on Hover - Outside table for proper HTML structure */}
      {hoveredItem && (
        <div className="fixed z-50 pointer-events-none">
          {(() => {
            const hoveredItemData = items.find(item => item.id === hoveredItem)
            if (!hoveredItemData) return null

            const statusConfig = getStatusConfig(hoveredItemData.status,
              hoveredItemData.borrowings && hoveredItemData.borrowings.some((borrowing: any) => borrowing.status === 'ACTIVE')
            )

            return (
              <div
                className="bg-white border border-gray-200 rounded-lg shadow-xl p-4 min-w-64 max-w-80"
                style={{
                  position: 'fixed',
                  left: '50%',
                  top: '50%',
                  transform: 'translate(-50%, -50%)',
                  zIndex: 1000
                }}
              >
                <div className="flex items-center space-x-3">
                  <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                    <Package className="h-8 w-8 text-gray-500" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900">{hoveredItemData.name}</h4>
                    <p className="text-sm text-gray-500">{hoveredItemData.category?.name}</p>
                    <p className="text-xs text-gray-400 mt-1">{hoveredItemData.description}</p>
                    <div className="flex items-center space-x-2 mt-2">
                      <div className={`w-2 h-2 rounded-full ${statusConfig.dotColor}`}></div>
                      <span className="text-xs text-gray-600">{statusConfig.label}</span>
                      <span className="text-xs text-gray-400">• Stok: {hoveredItemData.stock}</span>
                    </div>
                  </div>
                </div>
              </div>
            )
          })()}
        </div>
      )}
    </Card>
  )
}

export default InventoryTableView
