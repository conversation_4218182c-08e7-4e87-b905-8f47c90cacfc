'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { 
  Package, 
  Edit, 
  Trash2, 
  Download, 
  Upload, 
  Settings, 
  ArrowRight,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { Item, ItemStatus } from '@/types'

interface ActionConfig {
  label: string
  action: string
  variant: 'primary' | 'secondary' | 'danger'
  icon?: React.ReactNode
}

interface InventoryActionBarProps {
  selectedItems: Set<string>
  selectedItemsData: Item[]
  onAction: (action: string) => void
  isLoading: boolean
}

const InventoryActionBar: React.FC<InventoryActionBarProps> = ({
  selectedItems,
  selectedItemsData,
  onAction,
  isLoading
}) => {
  // Calculate available actions based on selection
  const getAvailableActions = (): ActionConfig[] => {
    const actions: ActionConfig[] = []

    if (selectedItems.size === 0) return []

    if (selectedItems.size === 1) {
      const item = selectedItemsData[0]
      const isLoaned = item.borrowings && item.borrowings.some((borrowing: any) => borrowing.status === 'ACTIVE')

      if (item.status === ItemStatus.AVAILABLE && !isLoaned) {
        actions.push({ 
          label: 'Pinjamkan', 
          action: 'loan', 
          variant: 'primary',
          icon: <ArrowRight className="h-4 w-4" />
        })
        actions.push({ 
          label: 'Atur Perbaikan', 
          action: 'maintenance', 
          variant: 'secondary',
          icon: <Settings className="h-4 w-4" />
        })
      } else if (isLoaned) {
        actions.push({ 
          label: 'Tandai Kembali', 
          action: 'return', 
          variant: 'primary',
          icon: <CheckCircle className="h-4 w-4" />
        })
        actions.push({ 
          label: 'Perpanjang', 
          action: 'extend', 
          variant: 'secondary',
          icon: <Clock className="h-4 w-4" />
        })
      }
    } else {
      // Multiple items selected
      const availableItems = selectedItemsData.filter(item => 
        item.status === ItemStatus.AVAILABLE && 
        (!item.borrowings || !item.borrowings.some((borrowing: any) => borrowing.status === 'ACTIVE'))
      )

      if (availableItems.length === selectedItems.size) {
        actions.push({ 
          label: 'Pinjamkan Item Terpilih', 
          action: 'multi-loan', 
          variant: 'primary',
          icon: <ArrowRight className="h-4 w-4" />
        })
      }
      actions.push({ 
        label: 'Edit Massal', 
        action: 'bulk-edit', 
        variant: 'secondary',
        icon: <Edit className="h-4 w-4" />
      })
    }

    // Common actions for all selections
    actions.push({ 
      label: 'Export Terpilih', 
      action: 'export-selected', 
      variant: 'secondary',
      icon: <Download className="h-4 w-4" />
    })

    if (selectedItems.size <= 10) { // Limit delete action for safety
      actions.push({ 
        label: selectedItems.size === 1 ? 'Hapus Item' : `Hapus ${selectedItems.size} Item`, 
        action: 'delete-selected', 
        variant: 'danger',
        icon: <Trash2 className="h-4 w-4" />
      })
    }

    return actions
  }

  const availableActions = getAvailableActions()

  if (selectedItems.size === 0) {
    return null
  }

  return (
    <Card className="glass border-l-4 border-l-blue-500">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          {/* Selection Info */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Package className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">
                  {selectedItems.size} item terpilih
                </p>
                <p className="text-sm text-gray-500">
                  {selectedItems.size === 1 
                    ? selectedItemsData[0]?.name 
                    : `${selectedItemsData.length} item dari berbagai kategori`
                  }
                </p>
              </div>
            </div>

            {/* Quick Stats for Multiple Selection */}
            {selectedItems.size > 1 && (
              <div className="hidden md:flex items-center space-x-4 text-sm text-gray-600">
                <div className="flex items-center space-x-1">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>
                    {selectedItemsData.filter(item => 
                      item.status === ItemStatus.AVAILABLE && 
                      (!item.borrowings || !item.borrowings.some((borrowing: any) => borrowing.status === 'ACTIVE'))
                    ).length} tersedia
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="h-4 w-4 text-yellow-500" />
                  <span>
                    {selectedItemsData.filter(item => 
                      item.borrowings && item.borrowings.some((borrowing: any) => borrowing.status === 'ACTIVE')
                    ).length} dipinjam
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <XCircle className="h-4 w-4 text-red-500" />
                  <span>
                    {selectedItemsData.filter(item => 
                      item.status === ItemStatus.OUT_OF_STOCK
                    ).length} perbaikan
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            {availableActions.map((actionConfig, index) => (
              <Button
                key={index}
                variant={actionConfig.variant}
                onClick={() => onAction(actionConfig.action)}
                disabled={isLoading}
                className="flex items-center space-x-2"
                size="sm"
              >
                {actionConfig.icon}
                <span className="hidden sm:inline">{actionConfig.label}</span>
              </Button>
            ))}

            {/* Clear Selection */}
            <Button
              variant="ghost"
              onClick={() => onAction('clear-selection')}
              className="text-gray-500 hover:text-gray-700"
              size="sm"
            >
              <XCircle className="h-4 w-4" />
              <span className="hidden sm:inline ml-1">Batal</span>
            </Button>
          </div>
        </div>

        {/* Progress Bar for Bulk Operations */}
        {isLoading && (
          <div className="mt-4">
            <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
              <span>Memproses {selectedItems.size} item...</span>
              <span>Mohon tunggu</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default InventoryActionBar
