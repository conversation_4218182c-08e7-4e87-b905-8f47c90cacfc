'use client'

import React, { useState, useEffect } from 'react'
import AppLayout from '@/components/layout/AppLayout'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { Download, FileText, BarChart3, Filter } from 'lucide-react'

type ReportType = 'borrowings' | 'inventory' | 'analytics'

interface ReportData {
  data: Record<string, unknown>[]
  summary?: {
    total: number
    active: number
    returned: number
    overdue: number
  }
}

const ReportsPage: React.FC = () => {
  const [reportType, setReportType] = useState<ReportType>('borrowings')
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: ''
  })

  useEffect(() => {
    const fetchReportData = async () => {
      setIsLoading(true)
      try {
        let url = `/api/${reportType}`
        const params = new URLSearchParams()

        if (dateRange.startDate) params.append('startDate', dateRange.startDate)
        if (dateRange.endDate) params.append('endDate', dateRange.endDate)

        if (params.toString()) {
          url += `?${params.toString()}`
        }

        const response = await fetch(url)
        if (response.ok) {
          const data = await response.json()

          switch (reportType) {
            case 'borrowings':
              const borrowings = data.data || []
              const transformedData = borrowings.flatMap((borrowing: Record<string, unknown>) =>
                ((borrowing.items as Record<string, unknown>[]) || []).map((item: Record<string, unknown>) => ({
                  id: borrowing.id,
                  borrowerName: borrowing.borrowerName,
                  itemName: (item.item as Record<string, unknown>)?.name || 'Unknown',
                  category: ((item.item as Record<string, unknown>)?.category as Record<string, unknown>)?.name || 'Unknown',
                  borrowDate: borrowing.borrowDate,
                  returnDate: borrowing.returnDate,
                  purpose: borrowing.purpose,
                  status: borrowing.status
                }))
              )

              setReportData({
                data: transformedData,
                summary: {
                  total: borrowings.length,
                  active: borrowings.filter((b: Record<string, unknown>) => b.status === 'ACTIVE').length,
                  returned: borrowings.filter((b: Record<string, unknown>) => b.status === 'RETURNED').length,
                  overdue: borrowings.filter((b: Record<string, unknown>) => b.status === 'OVERDUE').length
                }
              })
              break

            case 'inventory':
              setReportData({
                data: data.data || [],
                summary: {
                  total: data.data?.length || 0,
                  active: data.data?.filter((item: Record<string, unknown>) => item.status === 'AVAILABLE').length || 0,
                  returned: data.data?.filter((item: Record<string, unknown>) => (item.stock as number) > 0).length || 0,
                  overdue: data.data?.filter((item: Record<string, unknown>) => (item.stock as number) <= (item.minStock as number)).length || 0
                }
              })
              break

            default:
              setReportData({ data: data.data || [] })
          }
        }
      } catch (error) {
        console.error('Error fetching report data:', error)
        setReportData({ data: [] })
      } finally {
        setIsLoading(false)
      }
    }

    fetchReportData()
  }, [reportType, dateRange.startDate, dateRange.endDate])

  const handleRefreshData = () => {
    // Trigger a re-fetch by updating a dependency
    setDateRange(prev => ({ ...prev }))
  }

  const exportToCSV = () => {
    if (!reportData?.data.length) return

    const headers = Object.keys(reportData.data[0]).join(',')
    const rows = reportData.data.map(row => 
      Object.values(row).map(value => 
        typeof value === 'string' && value.includes(',') ? `"${value}"` : value
      ).join(',')
    ).join('\n')
    
    const csv = `${headers}\n${rows}`
    const blob = new Blob([csv], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${reportType}-report-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Laporan</h1>
            <p className="text-gray-600 mt-1">Generate dan export laporan sistem</p>
          </div>
          <Button
            onClick={exportToCSV}
            disabled={!reportData?.data.length}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            <Download className="w-4 h-4 mr-2" />
            Export CSV
          </Button>
        </div>

        {/* Report Type Selector */}
        <div className="flex gap-2">
          <Button
            variant={reportType === 'borrowings' ? 'primary' : 'outline'}
            onClick={() => setReportType('borrowings')}
          >
            <FileText className="w-4 h-4 mr-2" />
            Peminjaman
          </Button>
          <Button
            variant={reportType === 'inventory' ? 'primary' : 'outline'}
            onClick={() => setReportType('inventory')}
          >
            <BarChart3 className="w-4 h-4 mr-2" />
            Inventaris
          </Button>
          <Button
            variant={reportType === 'analytics' ? 'primary' : 'outline'}
            onClick={() => setReportType('analytics')}
          >
            <BarChart3 className="w-4 h-4 mr-2" />
            Analitik
          </Button>
        </div>

        {/* Date Range Filter */}
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Filter className="w-5 h-5" />
              <h3 className="font-semibold">Filter Tanggal</h3>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Dari Tanggal
                </label>
                <input
                  type="date"
                  value={dateRange.startDate}
                  onChange={(e) => setDateRange({ ...dateRange, startDate: e.target.value })}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Sampai Tanggal
                </label>
                <input
                  type="date"
                  value={dateRange.endDate}
                  onChange={(e) => setDateRange({ ...dateRange, endDate: e.target.value })}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="flex items-end">
                <Button onClick={handleRefreshData} disabled={isLoading}>
                  {isLoading ? 'Loading...' : 'Apply Filter'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Summary Cards */}
        {reportData?.summary && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-gray-900">{reportData.summary.total}</div>
                <div className="text-sm text-gray-600">Total</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-blue-600">{reportData.summary.active}</div>
                <div className="text-sm text-gray-600">Aktif</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-green-600">{reportData.summary.returned}</div>
                <div className="text-sm text-gray-600">Selesai</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-red-600">{reportData.summary.overdue}</div>
                <div className="text-sm text-gray-600">Terlambat</div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Report Data */}
        <Card>
          <CardContent className="p-0">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-gray-500">Memuat data...</div>
              </div>
            ) : !reportData?.data.length ? (
              <div className="text-center py-12">
                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Tidak ada data</h3>
                <p className="text-gray-500">Tidak ada data untuk periode yang dipilih</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b border-gray-200">
                    <tr>
                      {Object.keys(reportData.data[0]).map((key) => (
                        <th key={key} className="text-left py-3 px-4 font-medium text-gray-700 capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.data.map((row, index) => (
                      <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                        {Object.values(row).map((value, cellIndex) => (
                          <td key={cellIndex} className="py-3 px-4 text-gray-900">
                            {value instanceof Date ? value.toLocaleDateString('id-ID') : String(value)}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}

export default ReportsPage


