'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { Package, Eye, User, Check } from 'lucide-react'
import { Item, ItemStatus } from '@/types'

// Enhanced status system with visual indicators
const getStatusConfig = (status: ItemStatus, isLoaned = false) => {
  if (isLoaned) {
    return {
      label: 'Dipinjam',
      color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      dotColor: 'bg-yellow-400',
      bgColor: '#facc15'
    }
  }

  switch (status) {
    case ItemStatus.AVAILABLE:
      return {
        label: 'Tersedia',
        color: 'bg-green-100 text-green-800 border-green-200',
        dotColor: 'bg-green-400',
        bgColor: '#22c55e'
      }
    case ItemStatus.OUT_OF_STOCK:
      return {
        label: 'Perbaikan',
        color: 'bg-red-100 text-red-800 border-red-200',
        dotColor: 'bg-red-400',
        bgColor: '#ef4444'
      }
    case ItemStatus.DISCONTINUED:
      return {
        label: 'Pensiun',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        dotColor: 'bg-gray-400',
        bgColor: '#6b7280'
      }
    default:
      return {
        label: 'Unknown',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        dotColor: 'bg-gray-400',
        bgColor: '#6b7280'
      }
  }
}

interface InventoryCardViewProps {
  items: Item[]
  selectedItems: Set<string>
  onSelectItem: (itemId: string, checked: boolean) => void
  onOpenDetail: (item: Item) => void
}

const InventoryCardView: React.FC<InventoryCardViewProps> = ({
  items,
  selectedItems,
  onSelectItem,
  onOpenDetail
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
      {items.map((item) => {
        const statusConfig = getStatusConfig(item.status,
          item.borrowings && item.borrowings.some((borrowing: any) => borrowing.status === 'ACTIVE')
        )
        const isSelected = selectedItems.has(item.id)

        return (
          <Card
            key={item.id}
            className={`glass hover:shadow-xl transition-all duration-300 relative group cursor-pointer transform hover:-translate-y-1 ${
              isSelected ? 'ring-2 ring-blue-500 shadow-lg' : ''
            }`}
            onClick={() => onSelectItem(item.id, !isSelected)}
          >
            <CardContent className="p-0">
              {/* Checkbox - Top Left */}
              <div className="absolute top-3 left-3 z-10">
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={(e) => {
                    e.stopPropagation()
                    onSelectItem(item.id, e.target.checked)
                  }}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 w-4 h-4"
                />
              </div>

              {/* Status Indicator - Top Right */}
              <div className="absolute top-3 right-3 z-10">
                <div className="flex items-center space-x-1">
                  <div className={`w-2 h-2 rounded-full ${statusConfig.dotColor}`}></div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium border ${statusConfig.color}`}>
                    {statusConfig.label}
                  </span>
                </div>
              </div>

              {/* Item Image */}
              <div className="relative w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 rounded-t-xl flex items-center justify-center overflow-hidden">
                <Package className="h-16 w-16 text-gray-400 group-hover:text-gray-500 transition-colors" />

                {/* Hover Overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        onOpenDetail(item)
                      }}
                      className="bg-white/90 hover:bg-white shadow-lg"
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      Detail
                    </Button>
                  </div>
                </div>
              </div>

              {/* Item Details */}
              <div className="p-4 space-y-3">
                {/* Item Name */}
                <div>
                  <h3 className="font-semibold text-gray-900 text-lg truncate group-hover:text-blue-600 transition-colors">
                    {item.name}
                  </h3>
                  <p className="text-sm text-gray-500 line-clamp-2 mt-1">
                    {item.description || 'Tidak ada deskripsi'}
                  </p>
                </div>

                {/* Category & Location */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span className="text-sm text-gray-600 font-medium">
                      {item.category?.name || 'Kategori tidak diketahui'}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-sm text-gray-600">
                      {item.location?.name || 'Lokasi tidak diketahui'}
                    </span>
                  </div>
                </div>

                {/* Stock Info */}
                <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-500">Stok:</span>
                    <span className={`text-sm font-semibold ${
                      item.stock === 0 ? 'text-red-600' :
                      item.stock <= item.minStock ? 'text-yellow-600' :
                      'text-green-600'
                    }`}>
                      {item.stock}
                    </span>
                  </div>
                  <div className="text-xs text-gray-400">
                    Min: {item.minStock}
                  </div>
                </div>

                {/* Borrowing Info (if applicable) */}
                {item.borrowings && item.borrowings.some((borrowing: any) => borrowing.status === 'ACTIVE') && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-2">
                    <div className="flex items-center space-x-2">
                      <User className="h-3 w-3 text-yellow-600" />
                      <span className="text-xs text-yellow-800 font-medium">Sedang Dipinjam</span>
                    </div>
                    {item.borrowings
                      .filter((borrowing: any) => borrowing.status === 'ACTIVE')
                      .slice(0, 1)
                      .map((borrowing: any) => (
                        <div key={borrowing.id} className="mt-1">
                          <p className="text-xs text-yellow-700">
                            {borrowing.borrowerName || 'Peminjam tidak diketahui'}
                          </p>
                          <p className="text-xs text-yellow-600">
                            {borrowing.purpose || 'Tujuan tidak diketahui'}
                          </p>
                        </div>
                      ))}
                  </div>
                )}
              </div>

              {/* Selection Indicator */}
              {isSelected && (
                <div className="absolute inset-0 bg-blue-500 bg-opacity-5 rounded-xl pointer-events-none">
                  <div className="absolute bottom-2 right-2">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}

export default InventoryCardView
